<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "TVSeries",
    "name": "{{ $currentMovie->name }}",
    "url": "{{ $currentMovie->getUrl() }}",
    "image": "{{ $currentMovie->getThumbUrl() }}",
    "description": "Phim {{ $currentMovie->origin_name }} ({{ $currentMovie->publish_year }}) Vietsub, chất lượng HD. Tập {{ $currentMovie->episode_current }} hiện tại.",
    "director": [
        @foreach($currentMovie->directors as $director)
            {
                "@type": "Person",
                "name": "{{ $director->name }}",
                "url": "{{ $director->getUrl() }}"
            }@if (!$loop->last),@endif
        @endforeach
    ],
    "actor": [
        @foreach($currentMovie->actors as $actor)
            {
                "@type": "Person",
                "name": "{{ $actor->name }}",
                "url": "{{ $actor->getUrl() }}"
            }@if (!$loop->last),@endif
        @endforeach
    ],
    "genre": [
        @foreach($currentMovie->categories as $category)
            "{{ $category->name }}"@if (!$loop->last),@endif
        @endforeach
    ],
    "datePublished": "{{ $currentMovie->publish_year }}",
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "{{ $currentMovie->getRatingStar() }}",
        "ratingCount": "{{ $currentMovie->getRatingCount() }}"
    }
}
</script>
