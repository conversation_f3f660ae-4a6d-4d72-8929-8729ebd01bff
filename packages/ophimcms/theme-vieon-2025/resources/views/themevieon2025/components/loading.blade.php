@php
    // Đọc setting từ JSON
    $settingsRecord = \Backpack\Settings\app\Models\Setting::where('key', 'loading_theme_settings')->first();
    $settings = $settingsRecord ? json_decode($settingsRecord->value, true) : [];
    $enabled = $settings['enabled'] ?? 1;
    $duration = $settings['duration'] ?? 3; // Thời gian mặc định 3 giây
    
    if (!$enabled) return;
    
    $slug = $settings['active'] ?? 'tet-nguyen-dan';
    
    // Sử dụng model LoadingTheme từ database
    $theme = \Ophim\ThemeVieon2025\Models\LoadingTheme::findBySlug($slug);
    
    // Nếu không tìm thấy theme theo slug, thử lấy theme ngày lễ hoặc theme mặc định
    if (!$theme) {
        $theme = \Ophim\ThemeVieon2025\Models\LoadingTheme::getHolidayTheme() 
                ?? \Ophim\ThemeVieon2025\Models\LoadingTheme::getDefault();
    }
    
    // Fallback nếu vẫn không có theme nào
    if (!$theme) {
        $theme = (object) [
            'title' => config('app.name'),
            'subtitle' => '',
            'description' => '',
            'background_color' => '#23242a',
            'text_color' => '#ffffff',
            'accent_color' => 'var(--main-color, #00ff66)'
        ];
    }
    
    $app_name = config('app.name');
    $title = $theme->title ?? $app_name;
    $subtitle = $theme->subtitle ?? '';
    $description = $theme->description ?? '';
    $bg_color = $theme->background_color ?? '#23242a';
    $text_color = $theme->text_color ?? '#ffffff';
    $accent_color = $theme->accent_color ?? 'var(--main-color, #00ff66)';
    
    // Helper chuyển hex sang rgba cho màu mờ
    function hex2rgba($color, $alpha = 0.5) {
        $color = ltrim($color, '#');
        if (strlen($color) == 6) {
            $rgb = [
                hexdec(substr($color, 0, 2)),
                hexdec(substr($color, 2, 2)),
                hexdec(substr($color, 4, 2))
            ];
            return 'rgba(' . implode(',', $rgb) . ',' . $alpha . ')';
        }
        return $color;
    }
    $text_color_faded = hex2rgba($text_color, 0.5);
@endphp

@if($enabled)
<div id="global-loading"
     class="fixed inset-0 z-[10000] flex flex-col items-center justify-center transition-opacity duration-500 opacity-100"
     data-duration="{{ $duration }}"
     data-bg-color="{{ $bg_color }}"
     data-text-color="{{ $text_color }}"
     data-accent-color="{{ $accent_color }}"
     data-text-color-faded="{{ $text_color_faded }}">
    <div class="flex flex-col items-center">
        @include('themevieon2025::components.logo', ['class' => 'h-20 w-60 mb-4 animate-bounce'])
        <h1 class="text-3xl font-bold mb-2 flex items-center gap-2" id="loading-title">
            {{ $title }}
            @if($subtitle)
                <span class="font-semibold text-lg" id="loading-subtitle">| {{ $subtitle }}</span>
            @endif
        </h1>
        @if($description)
            <p class="text-lg font-semibold text-center max-w-xl" id="loading-description">
                {{ $description }}
            </p>
        @endif
    </div>
</div>
@endif

<script>
// Apply colors from data attributes
document.addEventListener('DOMContentLoaded', function() {
    const loader = document.getElementById('global-loading');
    if (loader) {
        // Apply background color
        loader.style.backgroundColor = loader.dataset.bgColor;
        
        // Apply text colors
        const title = document.getElementById('loading-title');
        if (title) title.style.color = loader.dataset.textColor;
        
        const subtitle = document.getElementById('loading-subtitle');
        if (subtitle) subtitle.style.color = loader.dataset.accentColor;
        
        const description = document.getElementById('loading-description');
        if (description) description.style.color = loader.dataset.textColorFaded;
    }
});

// Hide loading when page is loaded with custom duration
window.addEventListener('load', function() {
    const loader = document.getElementById('global-loading');
    if (loader) {
        // Sử dụng duration từ data attribute (mặc định 3 giây)
        const duration = parseInt(loader.dataset.duration || 3) * 1000; // Chuyển giây thành milliseconds
        
        setTimeout(() => {
            loader.style.opacity = 0;
            setTimeout(() => loader.style.display = 'none', 500);
        }, duration);
    }
});
</script> 