<?php

namespace Ophim\ThemeVieon2025\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

class AuthController extends Controller
{
    protected $redirectTo = '/';

    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Show login form
     */
    public function showLoginForm()
    {
        return view('themevieon2025::user.auth.login');
    }

    /**
     * Show registration form
     */
    public function showRegisterForm()
    {
        return view('themevieon2025::user.auth.register');
    }

    /**
     * Handle user login
     */
    public function login(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * Handle user registration
     */
    public function register(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * Handle user logout
     */
    public function logout(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * Change password
     */
    public function changePassword(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: Handle user login (JSON)
     */
    public function apiLogin(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: Handle user registration (JSON)
     */
    public function apiRegister(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: Handle user logout (JSON)
     */
    public function apiLogout(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: Update user profile (JSON)
     */
    public function apiUpdateProfile(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: Change password (JSON)
     */
    public function apiChangePassword(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: 2FA verify
     */
    public function api2faVerify(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: 2FA send
     */
    public function api2faSend(Request $request)
    {
        // This method is now handled by API
    }

    /**
     * API: resend email verification
     */
    public function apiVerificationResend(Request $request)
    {
        // This method is now handled by API
    }
}
