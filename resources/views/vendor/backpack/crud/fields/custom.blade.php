<div class="form-group col-md-4">
    <label class="mb-2">&nbsp;</label>
    <div id="{{ $field['name'] }}" class="btn btn-info d-flex justify-content-center align-items-end">{{ $field['button_label'] }}</div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    $(document).ready(function() {
        $("#loadingOverlay").hide();

        function getCategories(){
            let selectedCategories = [];
            $('input[name="categories[]"]:checked').each(function() {
                let labelText = $(this).parent('label').text().trim();
                if (labelText !== "") {
                    selectedCategories.push(labelText);
                }
            });
            return selectedCategories.join(', ');
        }

        function getMultipleSelectByName(name) {
            let list = $('select[name="'+ name +'[]"]').val();
            if (list.length > 0) {
                return list.join(', ');
            } else {
                return '';
            }
        }

        $("#{{ $field['name'] }}").click(function(e) {
            e.preventDefault();
            $("#loadingOverlay").show();

            let prompt = $('input[name=prompt]').val();
            if (prompt.trim() === '') {
                alert("Lỗi: Prompt không được để trống!");
                $("#loadingOverlay").hide();
                return;
            }
            // Lấy giá trị từ các trường nhập liệu
            let name = $("input[name=name]").val();
            let originName = $("input[name=origin_name]").val();
            let content = $("textarea[name=content]").val();
            let status = $("input[name=status]").val();
            let episodeTime = $("input[name=episode_time]").val();
            let episodeTotal = $("input[name=episode_total]").val();
            let categories = getCategories();
            let actors = getMultipleSelectByName('actors');
            let directors = getMultipleSelectByName('directors');
            let publishYear = $("input[name=publish_year]").val();

            let replacements = {
                "{name}": name,
                "{origin_name}": originName,
                "{content}": content,
                "{status}": status,
                "{episode_time}": episodeTime,
                "{episode_total}": episodeTotal,
                "{categories}": categories,
                "{actors}": actors,
                "{directors}": directors,
                "{publish_year}": publishYear,
            };

            for (let key in replacements) {
                prompt = prompt.replace(key, replacements[key]);
            }

            let id = $("input[name=id]").val();
            $.ajax({
                url: '/dangnhap/movie/gen-content',
                type: 'POST',
                data: {id, prompt},
                success: function(data) {
                    $('textarea[name=content]').summernote('code', data.content);
                    alert(data.notify)
                },
                error: function(xhr, textStatus, errorThrown) {
                    // Xử lý lỗi nếu có
                    $("#loadingOverlay").hide();
                    $("#ajax-error-frame").hide();
                    alert(JSON.parse(xhr.responseText));
                },
                complete: function() {
                    // Ẩn giao diện loading sau khi AJAX hoàn thành
                    $("#loadingOverlay").hide();
                    $("#ajax-error-frame").hide();
                }
            });
        });
    });

</script>
